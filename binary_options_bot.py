"""
Binary Options Trading Bot
Main controller for binary options trading using Deriv API
"""
import time
import argparse
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import signal
import sys
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live

from deriv_config import DerivConfig
from deriv_market_data import DerivMarketDataProvider
from binary_options_trader import BinaryOptionsTrader, ContractType
from ai_analyzer import AIMarketAnalyzer
from signal_generator import SignalGenerator, SignalType
from logger_config import setup_logger, log_error

class BinaryOptionsBot:
    """Main binary options trading bot controller"""
    
    def __init__(self):
        self.logger = setup_logger("BinaryOptionsBot")
        self.console = Console()
        
        # Initialize components
        self.market_data = DerivMarketDataProvider()
        self.trader = BinaryOptionsTrader()
        self.ai_analyzer = AIMarketAnalyzer()
        self.signal_generator = SignalGenerator()
        
        # Bot state
        self.is_running = False
        self.analysis_count = 0
        self.last_analysis_time = None
        
        # Setup graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info("[SHUTDOWN] Shutdown signal received, stopping bot...")
        self.stop()
        sys.exit(0)
    
    def start(self) -> bool:
        """Start the binary options bot"""
        try:
            self.console.print("[bold green]Starting Binary Options Bot[/bold green]")
            
            # Validate configuration
            if not DerivConfig.validate_config():
                self.console.print("[red]Configuration validation failed[/red]")
                return False
            
            # Start market data provider
            if not self.market_data.start():
                self.console.print("[red]Failed to start market data provider[/red]")
                return False
            
            # Start trader
            if not self.trader.start():
                self.console.print("[red]Failed to start binary options trader[/red]")
                return False
            
            self.is_running = True
            self.console.print("[green]Binary Options Bot started successfully[/green]")
            self.console.print(DerivConfig.get_trading_config_summary())
            
            return True
            
        except Exception as e:
            log_error(self.logger, e, "starting binary options bot")
            return False
    
    def stop(self):
        """Stop the binary options bot"""
        try:
            self.is_running = False
            self.trader.stop()
            self.market_data.stop()
            self.console.print("[yellow]Binary Options Bot stopped[/yellow]")
            
        except Exception as e:
            log_error(self.logger, e, "stopping binary options bot")
    
    def analyze_symbol(self, symbol: str, context: str = "") -> Optional[Dict]:
        """
        Analyze a symbol for binary options trading
        
        Args:
            symbol: Symbol to analyze
            context: Additional context for analysis
            
        Returns:
            Analysis result or None if failed
        """
        try:
            self.logger.info(f"Starting binary options analysis for {symbol}")
            
            # Check if connected
            if not self.market_data.is_connected():
                self.console.print("[red]ERROR: Not connected to Deriv API[/red]")
                return None
            
            # Get market data
            tick_history = self.market_data.get_tick_history(symbol, count=100)
            candle_history = self.market_data.get_candle_history(symbol, granularity=60, count=50)
            latest_tick = self.market_data.get_latest_tick(symbol)
            
            if tick_history is None or tick_history.empty:
                self.console.print(f"[red]ERROR: No tick data available for {symbol}[/red]")
                return None
            
            # Prepare market data for AI analysis
            market_data = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'quote': {
                    'price': latest_tick['price'] if latest_tick else tick_history.iloc[-1]['price'],
                    'timestamp': latest_tick['timestamp'] if latest_tick else tick_history.index[-1]
                },
                'tick_history': tick_history,
                'candle_history': candle_history,
                'is_synthetic': DerivConfig.is_synthetic_index(symbol),
                'is_fx': DerivConfig.is_forex_symbol(symbol),
                'symbol_info': DerivConfig.get_symbol_info(symbol)
            }
            
            # Get current trade state for context
            current_state = self.signal_generator.get_current_trade_state(symbol)
            entry_price = self.signal_generator.entry_prices.get(symbol, 0.0)
            
            trade_state_context = f"Current state: {current_state.value}"
            if entry_price > 0:
                current_price = market_data['quote']['price']
                pnl_pct = ((current_price - entry_price) / entry_price) * 100
                trade_state_context += f", Entry: {entry_price:.5f}, Current: {current_price:.5f}, P&L: {pnl_pct:+.2f}%"
            
            # AI analysis
            ai_analysis = self.ai_analyzer.analyze_market_data(
                market_data, 
                additional_context=context,
                trade_state_context=trade_state_context
            )
            
            if not ai_analysis:
                self.console.print(f"[red]ERROR: AI analysis failed for {symbol}[/red]")
                return None
            
            # Validate and enhance the analysis
            validated_analysis = self.ai_analyzer.validate_signal(ai_analysis, market_data)
            
            # Generate trading signal
            trading_signal = self.signal_generator.generate_signal(validated_analysis, market_data)
            
            if trading_signal:
                self.analysis_count += 1
                self.last_analysis_time = datetime.now()
                
                # Display signal
                self._display_signal(trading_signal, market_data)
                
                # Execute trade if signal is actionable
                if trading_signal.signal in [SignalType.CALL, SignalType.PUT]:
                    self._execute_binary_option(trading_signal)
            
            return {
                'signal': trading_signal,
                'market_data': market_data,
                'ai_analysis': validated_analysis
            }
            
        except Exception as e:
            log_error(self.logger, e, f"analyzing {symbol}")
            return None
    
    def _execute_binary_option(self, signal):
        """Execute a binary options trade based on signal"""
        try:
            if signal.signal == SignalType.CALL:
                contract_type = ContractType.CALL
            elif signal.signal == SignalType.PUT:
                contract_type = ContractType.PUT
            else:
                return
            
            # Place the trade
            binary_option = self.trader.place_trade(
                symbol=signal.symbol,
                contract_type=contract_type,
                duration=signal.duration or DerivConfig.DEFAULT_DURATION,
                duration_unit=signal.duration_unit or DerivConfig.DEFAULT_DURATION_UNIT,
                stake=DerivConfig.DEFAULT_STAKE
            )
            
            if binary_option:
                self.console.print(f"[green]✓ Trade executed: {contract_type.value} {signal.symbol}[/green]")
            else:
                self.console.print(f"[red]✗ Failed to execute trade[/red]")
                
        except Exception as e:
            log_error(self.logger, e, "executing binary option")
    
    def _display_signal(self, signal, market_data):
        """Display trading signal in console"""
        try:
            # Create signal display table
            table = Table(title=f"Binary Options Signal - {signal.symbol}")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="white")
            
            # Signal color based on type
            signal_color = "green" if signal.signal in [SignalType.CALL, SignalType.PUT] else "yellow"
            
            table.add_row("Signal", f"[{signal_color}]{signal.signal.value}[/{signal_color}]")
            table.add_row("Confidence", f"{signal.confidence:.1%}")
            table.add_row("Current Price", f"{market_data['quote']['price']:.5f}")
            table.add_row("Risk Level", signal.risk_level.value)
            
            if signal.duration:
                table.add_row("Duration", f"{signal.duration} {signal.duration_unit}")
            
            if signal.validation_flags:
                table.add_row("Flags", ", ".join(signal.validation_flags))
            
            self.console.print(table)
            
            # Display reasoning
            reasoning_panel = Panel(
                signal.reasoning,
                title="Analysis Reasoning",
                border_style="blue"
            )
            self.console.print(reasoning_panel)
            
        except Exception as e:
            log_error(self.logger, e, "displaying signal")
    
    def run_single_analysis(self, symbol: str = None, context: str = ""):
        """Run a single analysis cycle"""
        symbol = symbol or DerivConfig.BINARY_OPTIONS_SYMBOLS[0]
        
        self.console.print(f"\n[bold blue]Binary Options Analysis[/bold blue]")
        self.console.print(f"Symbol: {symbol}")
        self.console.print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.console.print("-" * 60)
        
        result = self.analyze_symbol(symbol, context)
        
        if result:
            # Update active contracts
            self.trader.update_active_contracts()
            
            # Display trading summary
            summary = self.trader.get_trading_summary()
            self.console.print(f"\n[bold]Trading Summary:[/bold]")
            self.console.print(f"Active Contracts: {summary['active_contracts']}")
            self.console.print(f"Total Trades: {summary['total_trades']}")
            self.console.print(f"Win Rate: {summary['win_rate']:.1f}%")
            self.console.print(f"Daily P&L: ${summary['net_daily_pnl']:.2f}")
        
        return result
    
    def run_continuous(self, symbols: List[str] = None, interval_minutes: int = 1):
        """Run bot continuously with scheduled analysis"""
        symbols = symbols or DerivConfig.BINARY_OPTIONS_SYMBOLS[:3]  # Limit to 3 symbols
        
        self.console.print(f"\n[bold green]Starting Continuous Binary Options Trading[/bold green]")
        self.console.print(f"Symbols: {', '.join(symbols)}")
        self.console.print(f"Interval: {interval_minutes} minutes")
        self.console.print("-" * 60)
        
        try:
            while self.is_running:
                for symbol in symbols:
                    if not self.is_running:
                        break
                    
                    self.analyze_symbol(symbol)
                    
                    # Update active contracts
                    self.trader.update_active_contracts()
                    
                    # Brief pause between symbols
                    time.sleep(5)
                
                # Wait for next cycle
                if self.is_running:
                    self.console.print(f"[dim]Waiting {interval_minutes} minutes for next cycle...[/dim]")
                    time.sleep(interval_minutes * 60)
                    
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Stopping continuous trading...[/yellow]")
        except Exception as e:
            log_error(self.logger, e, "continuous trading")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Binary Options Trading Bot")
    parser.add_argument('--symbol', '-s', type=str, help='Symbol to analyze')
    parser.add_argument('--continuous', '-c', action='store_true', help='Run continuously')
    parser.add_argument('--interval', '-i', type=int, default=1, help='Analysis interval in minutes')
    parser.add_argument('--context', type=str, default='', help='Additional context for analysis')
    
    args = parser.parse_args()
    
    # Create and start bot
    bot = BinaryOptionsBot()
    
    if not bot.start():
        print("Failed to start binary options bot")
        return
    
    try:
        if args.continuous:
            symbols = [args.symbol] if args.symbol else None
            bot.run_continuous(symbols, args.interval)
        else:
            bot.run_single_analysis(args.symbol, args.context)
    finally:
        bot.stop()

if __name__ == "__main__":
    main()
