# Binary Options Trading Bot Configuration
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Deriv API Configuration (REQUIRED for binary options)
DERIV_API_TOKEN=your_deriv_api_token_here
DERIV_APP_ID=1089

# Twelve Data API Configuration (Legacy - for traditional trading)
TWELVE_DATA_API_KEY=your_twelve_data_api_key_here

# Binary Options Trading Configuration
DEFAULT_STAKE=1.0
DEFAULT_DURATION=5
DEFAULT_DURATION_UNIT=t
MAX_STAKE_PER_TRADE=10.0
MAX_DAILY_LOSS=100.0
MAX_CONCURRENT_TRADES=3

# Binary Options Symbols (comma-separated)
BINARY_OPTIONS_SYMBOLS=R_10,R_25,R_50,EURUSD,GBPUSD

# Analysis Configuration
ENABLE_REAL_TIME_ANALYSIS=true
ANALYSIS_INTERVAL_SECONDS=30
TICK_HISTORY_COUNT=100
CANDLE_HISTORY_COUNT=50

# Legacy Configuration (for backward compatibility)
DEFAULT_SYMBOL=R_10
ANALYSIS_INTERVAL=1min
LOG_LEVEL=INFO
STOCK_SYMBOLS=AAPL,MSFT,GOOGL,TSLA,NVDA
FX_SYMBOLS=EUR/USD,GBP/USD,USD/JPY,AUD/USD
ENABLE_STOCKS=false
ENABLE_FX=false
SCALPING_MODE=false
SCALPING_INTERVAL=1min
INTRADAY_INTERVAL=5min
TWELVE_DATA_PREMIUM=false

# =============================================================================
# SETUP INSTRUCTIONS FOR BINARY OPTIONS TRADING
# =============================================================================
# 1. Get your Deriv API token from: https://app.deriv.com/account/api-token
# 2. Get your OpenAI API key from: https://platform.openai.com/api-keys
# 3. Copy this file to .env and replace the placeholder values
# 4. Run the binary options bot with: python binary_options_bot.py
#
# DERIV API FEATURES:
# - Real-time binary options trading
# - Synthetic indices (R_10, R_25, R_50, etc.)
# - Forex pairs (EURUSD, GBPUSD, etc.)
# - WebSocket-based real-time data
# - Demo and real money accounts available
#
# BINARY OPTIONS SYMBOLS:
# - R_10, R_25, R_50, R_75, R_100: Volatility indices
# - EURUSD, GBPUSD, USDJPY: Major forex pairs
# - Duration: 1-10 ticks (t) or 1-10 minutes (m)
#
# LEGACY TRADING (Traditional stocks/FX):
# - Use: python trading_bot.py (requires TWELVE_DATA_API_KEY)
# - Get Twelve Data API key from: https://twelvedata.com/pricing
