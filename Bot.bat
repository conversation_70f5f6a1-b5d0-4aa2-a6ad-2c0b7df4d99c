@echo off
title Binary Options Trading Bot - Deriv.com
color 0A

:MAIN_MENU
cls
echo.
echo ========================================
echo   BINARY OPTIONS TRADING BOT - DERIV
echo ========================================
echo.
echo Choose an option:
echo.
echo 1. Test Connection
echo 2. Single Analysis (R_10)
echo 3. Single Analysis (R_25) 
echo 4. Single Analysis (EURUSD)
echo 5. Single Analysis (GBPUSD)
echo 6. Custom Symbol Analysis
echo 7. Continuous Trading (1 min interval)
echo 8. Continuous Trading (5 min interval)
echo 9. View Configuration
echo 0. Exit
echo.
set /p choice="Enter your choice (0-9): "

if "%choice%"=="1" goto TEST_CONNECTION
if "%choice%"=="2" goto SINGLE_R10
if "%choice%"=="3" goto SINGLE_R25
if "%choice%"=="4" goto SINGLE_EURUSD
if "%choice%"=="5" goto SINGLE_GBPUSD
if "%choice%"=="6" goto CUSTOM_SYMBOL
if "%choice%"=="7" goto CONTINUOUS_1MIN
if "%choice%"=="8" goto CONTINUOUS_5MIN
if "%choice%"=="9" goto VIEW_CONFIG
if "%choice%"=="0" goto EXIT
goto INVALID_CHOICE

:TEST_CONNECTION
cls
echo.
echo ========================================
echo        TESTING DERIV CONNECTION
echo ========================================
echo.
echo Testing your Deriv API connection...
echo This will verify your API token and connectivity.
echo.
python test_deriv_connection.py
echo.
pause
goto MAIN_MENU

:SINGLE_R10
cls
echo.
echo ========================================
echo     ANALYZING R_10 (VOLATILITY 10)
echo ========================================
echo.
echo Running single analysis on Volatility 10 Index...
echo.
python binary_options_bot.py --symbol R_10
echo.
pause
goto MAIN_MENU

:SINGLE_R25
cls
echo.
echo ========================================
echo     ANALYZING R_25 (VOLATILITY 25)
echo ========================================
echo.
echo Running single analysis on Volatility 25 Index...
echo.
python binary_options_bot.py --symbol R_25
echo.
pause
goto MAIN_MENU

:SINGLE_EURUSD
cls
echo.
echo ========================================
echo        ANALYZING EURUSD FOREX
echo ========================================
echo.
echo Running single analysis on EUR/USD...
echo.
python binary_options_bot.py --symbol EURUSD
echo.
pause
goto MAIN_MENU

:SINGLE_GBPUSD
cls
echo.
echo ========================================
echo        ANALYZING GBPUSD FOREX
echo ========================================
echo.
echo Running single analysis on GBP/USD...
echo.
python binary_options_bot.py --symbol GBPUSD
echo.
pause
goto MAIN_MENU

:CUSTOM_SYMBOL
cls
echo.
echo ========================================
echo        CUSTOM SYMBOL ANALYSIS
echo ========================================
echo.
echo Available symbols:
echo - Volatility Indices: R_10, R_25, R_50, R_75, R_100
echo - Forex Pairs: EURUSD, GBPUSD, USDJPY, AUDUSD
echo.
set /p symbol="Enter symbol to analyze: "
if "%symbol%"=="" goto CUSTOM_SYMBOL
echo.
echo Running analysis on %symbol%...
echo.
python binary_options_bot.py --symbol %symbol%
echo.
pause
goto MAIN_MENU

:CONTINUOUS_1MIN
cls
echo.
echo ========================================
echo    CONTINUOUS TRADING (1 MINUTE)
echo ========================================
echo.
echo Starting continuous binary options trading...
echo Analysis interval: 1 minute
echo.
echo WARNING: This will start live trading!
echo Make sure you're using a demo account for testing.
echo.
set /p confirm="Continue? (y/n): "
if /i "%confirm%"=="y" (
    echo.
    echo Starting continuous trading...
    echo Press Ctrl+C to stop.
    echo.
    python binary_options_bot.py --continuous --interval 1
) else (
    echo Operation cancelled.
    timeout /t 2 >nul
)
goto MAIN_MENU

:CONTINUOUS_5MIN
cls
echo.
echo ========================================
echo    CONTINUOUS TRADING (5 MINUTES)
echo ========================================
echo.
echo Starting continuous binary options trading...
echo Analysis interval: 5 minutes
echo.
echo WARNING: This will start live trading!
echo Make sure you're using a demo account for testing.
echo.
set /p confirm="Continue? (y/n): "
if /i "%confirm%"=="y" (
    echo.
    echo Starting continuous trading...
    echo Press Ctrl+C to stop.
    echo.
    python binary_options_bot.py --continuous --interval 5
) else (
    echo Operation cancelled.
    timeout /t 2 >nul
)
goto MAIN_MENU

:VIEW_CONFIG
cls
echo.
echo ========================================
echo        CONFIGURATION STATUS
echo ========================================
echo.
echo Checking your configuration...
echo.
if exist .env (
    echo ✓ .env file found
    echo.
    echo Configuration preview:
    findstr /B "DERIV_APP_ID=" .env 2>nul || echo   DERIV_APP_ID: Not set
    findstr /B "DERIV_API_TOKEN=" .env 2>nul | findstr /V "your_deriv_api_token_here" >nul && echo   DERIV_API_TOKEN: ✓ Set || echo   DERIV_API_TOKEN: ❌ Not configured
    findstr /B "OPENAI_API_KEY=" .env 2>nul | findstr /V "your_openai_api_key_here" >nul && echo   OPENAI_API_KEY: ✓ Set || echo   OPENAI_API_KEY: ❌ Not configured
    findstr /B "DEFAULT_STAKE=" .env 2>nul || echo   DEFAULT_STAKE: Using default
    findstr /B "BINARY_OPTIONS_SYMBOLS=" .env 2>nul || echo   BINARY_OPTIONS_SYMBOLS: Using default
) else (
    echo ❌ .env file not found!
    echo.
    echo Please copy .env.example to .env and configure your API keys.
)
echo.
echo For setup instructions, see BINARY_OPTIONS_README.md
echo.
pause
goto MAIN_MENU

:INVALID_CHOICE
cls
echo.
echo ========================================
echo           INVALID CHOICE
echo ========================================
echo.
echo Please enter a number between 0 and 9.
echo.
timeout /t 2 >nul
goto MAIN_MENU

:EXIT
cls
echo.
echo ========================================
echo        BINARY OPTIONS BOT EXIT
echo ========================================
echo.
echo Thank you for using the Binary Options Trading Bot!
echo.
echo Remember:
echo - Always test with demo accounts first
echo - Binary options trading involves significant risk
echo - Only trade with money you can afford to lose
echo.
echo Visit https://developers.deriv.com for more information.
echo.
timeout /t 3 >nul
exit
