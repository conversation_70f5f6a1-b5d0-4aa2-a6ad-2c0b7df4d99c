"""
Deriv Market Data Provider
Provides market data specifically for Deriv binary options trading
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
import threading
import time

from deriv_api import DerivAPIClient, DerivResponse
from deriv_config import DerivConfig
from logger_config import setup_logger, log_error

class DerivMarketDataProvider:
    """
    Market data provider for Deriv binary options
    Handles real-time ticks, historical data, and market analysis
    """
    
    def __init__(self):
        self.logger = setup_logger("DerivMarketData")
        self.api_client = DerivAPIClient()
        
        # Data storage
        self.tick_data: Dict[str, List[Dict]] = {}
        self.candle_data: Dict[str, pd.DataFrame] = {}
        self.latest_ticks: Dict[str, Dict] = {}
        
        # Subscriptions
        self.active_subscriptions: Dict[str, str] = {}
        
        # Thread safety
        self.data_lock = threading.Lock()
        
        # Real-time data handlers
        self.tick_handlers: Dict[str, List[Callable]] = {}
        
    def start(self):
        """Start the market data provider"""
        try:
            self.api_client.start()
            
            # Register message handlers
            self.api_client.register_message_handler("tick", self._handle_tick_data)
            self.api_client.register_message_handler("candles", self._handle_candle_data)
            self.api_client.register_message_handler("ohlc", self._handle_candle_data)
            
            self.logger.info("Deriv market data provider started")
            return True
            
        except Exception as e:
            log_error(self.logger, e, "starting market data provider")
            return False
    
    def stop(self):
        """Stop the market data provider"""
        try:
            # Unsubscribe from all active subscriptions
            for symbol, subscription_id in self.active_subscriptions.items():
                self.api_client.unsubscribe(subscription_id)
            
            self.api_client.stop()
            self.logger.info("Deriv market data provider stopped")
            
        except Exception as e:
            log_error(self.logger, e, "stopping market data provider")
    
    def is_connected(self) -> bool:
        """Check if connected to Deriv API"""
        return self.api_client.is_connected()
    
    def get_active_symbols(self) -> Optional[List[Dict]]:
        """Get list of active trading symbols"""
        try:
            response = self.api_client.get_active_symbols()
            if response and not response.error:
                symbols = response.data.get('active_symbols', [])
                
                # Filter for binary options compatible symbols
                binary_symbols = []
                for symbol in symbols:
                    if symbol.get('exchange_is_open') and symbol.get('is_trading_suspended') == 0:
                        binary_symbols.append({
                            'symbol': symbol.get('symbol'),
                            'display_name': symbol.get('display_name'),
                            'market': symbol.get('market'),
                            'submarket': symbol.get('submarket'),
                            'pip': symbol.get('pip', 0.0001)
                        })
                
                self.logger.info(f"Retrieved {len(binary_symbols)} active symbols")
                return binary_symbols
            else:
                self.logger.error(f"Failed to get active symbols: {response.error if response else 'No response'}")
                return None
                
        except Exception as e:
            log_error(self.logger, e, "getting active symbols")
            return None
    
    def get_symbol_contracts(self, symbol: str) -> Optional[Dict]:
        """Get available contracts for a symbol"""
        try:
            response = self.api_client.get_contracts_for_symbol(symbol)
            if response and not response.error:
                contracts = response.data.get('contracts_for', {})
                
                # Extract binary options contracts
                available_contracts = contracts.get('available', [])
                binary_contracts = []
                
                for contract in available_contracts:
                    if contract.get('contract_type') in ['CALL', 'PUT', 'CALLE', 'PUTE']:
                        binary_contracts.append({
                            'contract_type': contract.get('contract_type'),
                            'contract_display': contract.get('contract_display'),
                            'min_contract_duration': contract.get('min_contract_duration'),
                            'max_contract_duration': contract.get('max_contract_duration'),
                            'durations': contract.get('durations', [])
                        })
                
                return {
                    'symbol': symbol,
                    'contracts': binary_contracts,
                    'spot': contracts.get('spot'),
                    'pip': contracts.get('pip', 0.0001)
                }
            else:
                self.logger.error(f"Failed to get contracts for {symbol}: {response.error if response else 'No response'}")
                return None
                
        except Exception as e:
            log_error(self.logger, e, f"getting contracts for {symbol}")
            return None
    
    def subscribe_to_symbol(self, symbol: str, callback: Callable = None) -> bool:
        """Subscribe to real-time tick data for a symbol"""
        try:
            if symbol in self.active_subscriptions:
                self.logger.warning(f"Already subscribed to {symbol}")
                return True
            
            # Add callback to handlers
            if callback:
                if symbol not in self.tick_handlers:
                    self.tick_handlers[symbol] = []
                self.tick_handlers[symbol].append(callback)
            
            subscription_id = self.api_client.subscribe_to_ticks(symbol)
            if subscription_id:
                self.active_subscriptions[symbol] = subscription_id
                self.logger.info(f"Subscribed to {symbol} ticks")
                return True
            else:
                self.logger.error(f"Failed to subscribe to {symbol}")
                return False
                
        except Exception as e:
            log_error(self.logger, e, f"subscribing to {symbol}")
            return False
    
    def unsubscribe_from_symbol(self, symbol: str) -> bool:
        """Unsubscribe from a symbol"""
        try:
            if symbol not in self.active_subscriptions:
                self.logger.warning(f"Not subscribed to {symbol}")
                return True
            
            subscription_id = self.active_subscriptions[symbol]
            response = self.api_client.unsubscribe(subscription_id)
            
            if response and not response.error:
                del self.active_subscriptions[symbol]
                self.tick_handlers.pop(symbol, None)
                self.logger.info(f"Unsubscribed from {symbol}")
                return True
            else:
                self.logger.error(f"Failed to unsubscribe from {symbol}")
                return False
                
        except Exception as e:
            log_error(self.logger, e, f"unsubscribing from {symbol}")
            return False
    
    def get_latest_tick(self, symbol: str) -> Optional[Dict]:
        """Get the latest tick for a symbol"""
        with self.data_lock:
            return self.latest_ticks.get(symbol)
    
    def get_tick_history(self, symbol: str, count: int = 100) -> Optional[pd.DataFrame]:
        """Get historical tick data"""
        try:
            response = self.api_client.get_tick_history(symbol, count)
            if response and not response.error:
                history = response.data.get('history', {})
                times = history.get('times', [])
                prices = history.get('prices', [])
                
                if times and prices:
                    df = pd.DataFrame({
                        'timestamp': pd.to_datetime(times, unit='s'),
                        'price': prices
                    })
                    df.set_index('timestamp', inplace=True)
                    
                    self.logger.info(f"Retrieved {len(df)} ticks for {symbol}")
                    return df
                else:
                    self.logger.warning(f"No tick history data for {symbol}")
                    return None
            else:
                self.logger.error(f"Failed to get tick history for {symbol}: {response.error if response else 'No response'}")
                return None
                
        except Exception as e:
            log_error(self.logger, e, f"getting tick history for {symbol}")
            return None
    
    def get_candle_history(self, symbol: str, granularity: int = 60, count: int = 50) -> Optional[pd.DataFrame]:
        """Get historical candle data"""
        try:
            response = self.api_client.get_candle_history(symbol, granularity, count)
            if response and not response.error:
                candles = response.data.get('candles', [])
                
                if candles:
                    df = pd.DataFrame(candles)
                    df['timestamp'] = pd.to_datetime(df['epoch'], unit='s')
                    df.set_index('timestamp', inplace=True)
                    
                    # Rename columns to standard OHLC format
                    df = df.rename(columns={
                        'open': 'open',
                        'high': 'high', 
                        'low': 'low',
                        'close': 'close'
                    })
                    
                    # Select only OHLC columns
                    df = df[['open', 'high', 'low', 'close']]
                    
                    self.logger.info(f"Retrieved {len(df)} candles for {symbol}")
                    return df
                else:
                    self.logger.warning(f"No candle history data for {symbol}")
                    return None
            else:
                self.logger.error(f"Failed to get candle history for {symbol}: {response.error if response else 'No response'}")
                return None
                
        except Exception as e:
            log_error(self.logger, e, f"getting candle history for {symbol}")
            return None
    
    def _handle_tick_data(self, response: DerivResponse):
        """Handle incoming tick data"""
        try:
            tick_data = response.data.get('tick', {})
            symbol = tick_data.get('symbol')
            
            if symbol:
                tick = {
                    'symbol': symbol,
                    'price': float(tick_data.get('quote', 0)),
                    'timestamp': datetime.fromtimestamp(tick_data.get('epoch', 0)),
                    'bid': float(tick_data.get('bid', 0)),
                    'ask': float(tick_data.get('ask', 0))
                }
                
                with self.data_lock:
                    # Store latest tick
                    self.latest_ticks[symbol] = tick
                    
                    # Store in tick history
                    if symbol not in self.tick_data:
                        self.tick_data[symbol] = []
                    
                    self.tick_data[symbol].append(tick)
                    
                    # Keep only last 1000 ticks per symbol
                    if len(self.tick_data[symbol]) > 1000:
                        self.tick_data[symbol] = self.tick_data[symbol][-1000:]
                
                # Call registered handlers
                if symbol in self.tick_handlers:
                    for handler in self.tick_handlers[symbol]:
                        try:
                            handler(tick)
                        except Exception as e:
                            log_error(self.logger, e, f"calling tick handler for {symbol}")
                            
        except Exception as e:
            log_error(self.logger, e, "handling tick data")
    
    def _handle_candle_data(self, response: DerivResponse):
        """Handle incoming candle data"""
        try:
            # Implementation for real-time candle updates
            candle_data = response.data.get('ohlc', response.data.get('candles', {}))
            symbol = candle_data.get('symbol')
            
            if symbol and candle_data:
                self.logger.debug(f"Received candle data for {symbol}")
                
        except Exception as e:
            log_error(self.logger, e, "handling candle data")
