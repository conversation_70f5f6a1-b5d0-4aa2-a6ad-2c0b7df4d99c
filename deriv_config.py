"""
Deriv API Configuration
Configuration settings specific to Deriv.com binary options trading
"""
import os
from typing import List, Dict
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class DerivConfig:
    """Configuration class for Deriv API integration"""
    
    # API Configuration
    DERIV_APP_ID: str = os.getenv('DERIV_APP_ID', '1089')  # Default demo app ID
    DERIV_API_TOKEN: str = os.getenv('DERIV_API_TOKEN', '')  # User's API token
    DERIV_WEBSOCKET_URL: str = f"wss://ws.derivws.com/websockets/v3?app_id={DERIV_APP_ID}"

    # OpenAI Configuration
    OPENAI_API_KEY: str = os.getenv('OPENAI_API_KEY', '')
    OPENAI_MODEL: str = os.getenv('OPENAI_MODEL', 'gpt-4')
    OPENAI_MAX_TOKENS: int = int(os.getenv('OPENAI_MAX_TOKENS', '1000'))
    OPENAI_TEMPERATURE: float = float(os.getenv('OPENAI_TEMPERATURE', '0.3'))
    
    # Trading Configuration
    DEFAULT_STAKE: float = float(os.getenv('DEFAULT_STAKE', '1.0'))  # Default stake amount
    DEFAULT_DURATION: int = int(os.getenv('DEFAULT_DURATION', '5'))  # Default duration in ticks/minutes
    DEFAULT_DURATION_UNIT: str = os.getenv('DEFAULT_DURATION_UNIT', 't')  # 't' for ticks, 'm' for minutes
    
    # Binary Options Configuration
    BINARY_OPTIONS_SYMBOLS: List[str] = os.getenv(
        'BINARY_OPTIONS_SYMBOLS', 
        'R_10,R_25,R_50,R_75,R_100,EURUSD,GBPUSD,USDJPY'
    ).split(',')
    
    # Contract Types for Binary Options
    CONTRACT_TYPES: Dict[str, str] = {
        'CALL': 'CALL',      # Rise/Higher
        'PUT': 'PUT',        # Fall/Lower
        'CALLE': 'CALLE',    # Rise with equals
        'PUTE': 'PUTE'       # Fall with equals
    }
    
    # Risk Management
    MAX_STAKE_PER_TRADE: float = float(os.getenv('MAX_STAKE_PER_TRADE', '10.0'))
    MAX_DAILY_LOSS: float = float(os.getenv('MAX_DAILY_LOSS', '100.0'))
    MAX_CONCURRENT_TRADES: int = int(os.getenv('MAX_CONCURRENT_TRADES', '3'))
    
    # Market Data Configuration
    TICK_HISTORY_COUNT: int = int(os.getenv('TICK_HISTORY_COUNT', '100'))
    CANDLE_HISTORY_COUNT: int = int(os.getenv('CANDLE_HISTORY_COUNT', '50'))
    
    # Analysis Configuration
    ENABLE_REAL_TIME_ANALYSIS: bool = os.getenv('ENABLE_REAL_TIME_ANALYSIS', 'true').lower() == 'true'
    ANALYSIS_INTERVAL_SECONDS: int = int(os.getenv('ANALYSIS_INTERVAL_SECONDS', '30'))
    
    # Synthetic Indices Configuration (Deriv's unique offerings)
    SYNTHETIC_INDICES: Dict[str, Dict] = {
        'R_10': {'name': 'Volatility 10 Index', 'volatility': 'low'},
        'R_25': {'name': 'Volatility 25 Index', 'volatility': 'medium'},
        'R_50': {'name': 'Volatility 50 Index', 'volatility': 'high'},
        'R_75': {'name': 'Volatility 75 Index', 'volatility': 'very_high'},
        'R_100': {'name': 'Volatility 100 Index', 'volatility': 'extreme'},
        '1HZ10V': {'name': 'Volatility 10 (1s) Index', 'volatility': 'low'},
        '1HZ25V': {'name': 'Volatility 25 (1s) Index', 'volatility': 'medium'},
        '1HZ50V': {'name': 'Volatility 50 (1s) Index', 'volatility': 'high'},
        '1HZ75V': {'name': 'Volatility 75 (1s) Index', 'volatility': 'very_high'},
        '1HZ100V': {'name': 'Volatility 100 (1s) Index', 'volatility': 'extreme'}
    }
    
    @classmethod
    def is_synthetic_index(cls, symbol: str) -> bool:
        """Check if symbol is a synthetic index"""
        return symbol in cls.SYNTHETIC_INDICES
    
    @classmethod
    def is_forex_symbol(cls, symbol: str) -> bool:
        """Check if symbol is a forex pair"""
        forex_symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD']
        return symbol in forex_symbols
    
    @classmethod
    def get_symbol_info(cls, symbol: str) -> Dict:
        """Get information about a trading symbol"""
        if cls.is_synthetic_index(symbol):
            return cls.SYNTHETIC_INDICES[symbol]
        elif cls.is_forex_symbol(symbol):
            return {'name': f'{symbol} Forex Pair', 'type': 'forex'}
        else:
            return {'name': symbol, 'type': 'unknown'}
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate essential configuration"""
        if not cls.DERIV_API_TOKEN:
            print("WARNING: DERIV_API_TOKEN not set. Trading will not be possible.")
            return False
        
        if cls.DEFAULT_STAKE <= 0:
            print("ERROR: DEFAULT_STAKE must be greater than 0")
            return False
            
        if cls.MAX_STAKE_PER_TRADE < cls.DEFAULT_STAKE:
            print("ERROR: MAX_STAKE_PER_TRADE must be >= DEFAULT_STAKE")
            return False
            
        return True
    
    @classmethod
    def get_trading_config_summary(cls) -> str:
        """Get a summary of current trading configuration"""
        return f"""
Deriv Trading Configuration:
- App ID: {cls.DERIV_APP_ID}
- Default Stake: ${cls.DEFAULT_STAKE}
- Default Duration: {cls.DEFAULT_DURATION} {cls.DEFAULT_DURATION_UNIT}
- Max Stake per Trade: ${cls.MAX_STAKE_PER_TRADE}
- Max Daily Loss: ${cls.MAX_DAILY_LOSS}
- Max Concurrent Trades: {cls.MAX_CONCURRENT_TRADES}
- Symbols: {', '.join(cls.BINARY_OPTIONS_SYMBOLS[:5])}{'...' if len(cls.BINARY_OPTIONS_SYMBOLS) > 5 else ''}
- Real-time Analysis: {'Enabled' if cls.ENABLE_REAL_TIME_ANALYSIS else 'Disabled'}
"""
